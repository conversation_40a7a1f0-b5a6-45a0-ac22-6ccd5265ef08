# PowerShell script to test document reading functionality

Write-Host "Testing MyCom Document Reading..." -ForegroundColor Green
Write-Host ""

try {
    # Start Word
    Write-Host "Starting Word application..." -ForegroundColor Yellow
    $wordApp = New-Object -ComObject Word.Application
    $wordApp.Visible = $true
    
    # Create a new document with test content
    Write-Host "Creating test document..." -ForegroundColor Yellow
    $doc = $wordApp.Documents.Add()
    
    # Add test content with Chinese and English
    $testContent = "This is a test document for MyCom Office Add-in.`n`n" +
                   "这是一个测试文档，包含中文内容。`n`n" +
                   "English words: hello world test document`n" +
                   "中文字符：测试文档插件功能`n`n" +
                   "Mixed content: This document has 这个文档有 mixed text 混合文本.`n`n" +
                   "Numbers and symbols: 123 456 !@# $%^"
    
    $doc.Content.Text = $testContent
    
    Write-Host "✅ Test document created!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Document content:" -ForegroundColor Cyan
    Write-Host $testContent -ForegroundColor White
    Write-Host ""
    
    # Create COM object and test
    Write-Host "Creating MyCom COM object..." -ForegroundColor Yellow
    $comObject = New-Object -ComObject "MyCom.OfficeAddin"
    
    if ($comObject) {
        Write-Host "✅ COM object created successfully!" -ForegroundColor Green
        
        # Connect to Word
        Write-Host "Connecting add-in to Word..." -ForegroundColor Yellow
        $comObject.OnConnection($wordApp, 0, $null, $null)
        
        # Wait for initialization
        Start-Sleep -Seconds 2
        
        # Trigger Word features
        Write-Host "Triggering Word features..." -ForegroundColor Yellow
        $comObject.TriggerWordFeatures()
        
        Write-Host "✅ Word features triggered!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Expected results:" -ForegroundColor Cyan
        Write-Host "1. A statistics dialog should appear" -ForegroundColor White
        Write-Host "2. Chinese characters should be counted" -ForegroundColor White
        Write-Host "3. English words should be counted" -ForegroundColor White
        Write-Host "4. Check the log file for detailed information" -ForegroundColor White
        
        # Clean up COM object
        [System.Runtime.InteropServices.Marshal]::ReleaseComObject($comObject) | Out-Null
        
    } else {
        Write-Host "❌ Failed to create COM object" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Check if statistics dialog appeared" -ForegroundColor White
    Write-Host "2. Verify the character counts are correct" -ForegroundColor White
    Write-Host "3. Check log file: C:\temp\MyCom_debug.log" -ForegroundColor White
    Write-Host "4. Look for any error messages" -ForegroundColor White
    Write-Host ""
    Write-Host "Expected counts for this test document:" -ForegroundColor Cyan
    Write-Host "- Chinese characters: approximately 15-20" -ForegroundColor White
    Write-Host "- English words: approximately 20-25" -ForegroundColor White
    Write-Host ""
    Write-Host "Press any key to continue..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
} catch {
    Write-Host "❌ Error during testing:" -ForegroundColor Red
    Write-Host "   $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure Word is installed" -ForegroundColor White
    Write-Host "2. Check if the add-in is registered" -ForegroundColor White
    Write-Host "3. Run TestCOM.ps1 to verify COM registration" -ForegroundColor White
}

Write-Host ""
Write-Host "Testing completed." -ForegroundColor Green
