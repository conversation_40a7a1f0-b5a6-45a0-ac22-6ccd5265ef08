// compreg.cpp : Implementation of COfficeAddin

#include "pch.h"
#include "framework.h"
#include "compreg.h"
#include <comdef.h>
#include <fstream>
#include <ctime>

// Simple logging function
void LogMessage(const wchar_t* message)
{
	std::wofstream logFile(L"C:\\temp\\MyCom_debug.log", std::ios::app);
	if (logFile.is_open())
	{
		time_t now = time(0);
		wchar_t timeStr[100];
		struct tm timeinfo;
		localtime_s(&timeinfo, &now);
		wcsftime(timeStr, sizeof(timeStr)/sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);

		logFile << timeStr << L" - " << message << std::endl;
		logFile.close();
	}
}

// COfficeAddin

STDMETHODIMP COfficeAddin::OnConnection(IDispatch* Application, long ConnectMode, IDispatch* AddInInst, SAFEARRAY** custom)
{
	LogMessage(L"OnConnection called!");

	HRESULT hr = S_OK;

	try
	{
		// Store references to the application and add-in instance
		if (Application)
		{
			m_pApplication = Application;
			m_pApplication->AddRef();
		}

		if (AddInInst)
		{
			m_pAddInInstance = AddInInst;
			m_pAddInInstance->AddRef();
		}

		// Show a simple message to indicate the add-in has loaded
		MessageBox(NULL, L"MyCom Office Add-in Connected Successfully!", L"MyCom Add-in", MB_OK | MB_ICONINFORMATION | MB_TOPMOST);

		// Detect which Office application we're connecting to and setup features
		hr = DetectOfficeApplication();
		if (FAILED(hr))
		{
			LogMessage(L"Failed to detect Office application, but continuing...");
			hr = S_OK; // Don't fail the connection for this
		}
	}
	catch (...)
	{
		LogMessage(L"Exception in OnConnection");
		hr = E_FAIL;
	}

	return hr;
}

STDMETHODIMP COfficeAddin::OnDisconnection(long RemoveMode, SAFEARRAY** custom)
{
	// Clean up resources
	if (m_pApplication)
	{
		m_pApplication->Release();
		m_pApplication = nullptr;
	}

	if (m_pAddInInstance)
	{
		m_pAddInInstance->Release();
		m_pAddInInstance = nullptr;
	}

	return S_OK;
}

STDMETHODIMP COfficeAddin::OnAddInsUpdate(SAFEARRAY** custom)
{
	return S_OK;
}

STDMETHODIMP COfficeAddin::OnStartupComplete(SAFEARRAY** custom)
{
	LogMessage(L"OnStartupComplete called");

	// Check if we're in Word and if there's an active document
	if (m_pWordApp)
	{
		DISPID dispid;
		OLECHAR* szMember = L"ActiveDocument";
		HRESULT hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
			VARIANT result;
			VariantInit(&result);

			hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
			if (SUCCEEDED(hr) && result.vt == VT_DISPATCH && result.pdispVal != nullptr)
			{
				LogMessage(L"Active document found in OnStartupComplete, triggering document open event");
				OnDocumentOpen();
			}

			VariantClear(&result);
		}
	}

	return S_OK;
}

STDMETHODIMP COfficeAddin::OnBeginShutdown(SAFEARRAY** custom)
{
	return S_OK;
}

STDMETHODIMP COfficeAddin::TriggerWordFeatures()
{
	LogMessage(L"TriggerWordFeatures called manually");

	if (m_pWordApp)
	{
		return OnDocumentOpen();
	}
	else
	{
		LogMessage(L"Word application not available");
		return E_FAIL;
	}
}

HRESULT COfficeAddin::DetectOfficeApplication()
{
	if (!m_pApplication)
		return E_FAIL;

	HRESULT hr = S_OK;

	try
	{
		LogMessage(L"Detecting Office application...");

		// Get the application name to determine which Office app we're in
		DISPID dispid;
		OLECHAR* szMember = L"Name";
		hr = m_pApplication->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);

		if (SUCCEEDED(hr))
		{
			DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
			VARIANT result;
			VariantInit(&result);

			hr = m_pApplication->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);

			if (SUCCEEDED(hr) && result.vt == VT_BSTR)
			{
				_bstr_t appName = result.bstrVal;
				const wchar_t* appNameStr = static_cast<const wchar_t*>(appName);

				wchar_t logMsg[256];
				swprintf_s(logMsg, L"Detected application: %s", appNameStr);
				LogMessage(logMsg);

				// Setup features based on the application
				if (wcsstr(appNameStr, L"Word") || wcsstr(appNameStr, L"Microsoft Word"))
				{
					LogMessage(L"Setting up Word features...");
					hr = SetupWordFeatures();
				}
				else if (wcsstr(appNameStr, L"Excel") || wcsstr(appNameStr, L"Microsoft Excel"))
				{
					LogMessage(L"Setting up Excel features...");
					hr = SetupExcelFeatures();
				}
				else if (wcsstr(appNameStr, L"PowerPoint") || wcsstr(appNameStr, L"Microsoft PowerPoint"))
				{
					LogMessage(L"Setting up PowerPoint features...");
					hr = SetupPowerPointFeatures();
				}
				else
				{
					LogMessage(L"Unknown Office application");
				}
			}
			else
			{
				LogMessage(L"Failed to get application name");
			}

			VariantClear(&result);
		}
		else
		{
			LogMessage(L"Failed to get Name property");
		}
	}
	catch (...)
	{
		LogMessage(L"Exception in DetectOfficeApplication");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::SetupWordFeatures()
{
	LogMessage(L"Setting up Word features...");

	HRESULT hr = S_OK;

	try
	{
		// Store Word application reference
		if (m_pApplication)
		{
			m_pWordApp = m_pApplication;
			m_pWordApp->AddRef();
		}

		// Setup event handlers to monitor document events
		hr = SetupWordEventHandlers();
		if (FAILED(hr))
		{
			LogMessage(L"Failed to setup Word event handlers, continuing...");
		}

		// Check if there's already an active document
		DISPID dispid;
		OLECHAR* szMember = L"ActiveDocument";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
			VARIANT result;
			VariantInit(&result);

			hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
			if (SUCCEEDED(hr) && result.vt == VT_DISPATCH && result.pdispVal != nullptr)
			{
				LogMessage(L"Active document found, triggering document open event");
				OnDocumentOpen();
			}
			else
			{
				LogMessage(L"No active document, waiting for document to be opened");
			}

			VariantClear(&result);
		}

		LogMessage(L"Word features setup completed");
	}
	catch (...)
	{
		LogMessage(L"Exception in SetupWordFeatures");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::SetupWordEventHandlers()
{
	LogMessage(L"Setting up Word event handlers...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		// For simplicity, we'll use a timer-based approach to check for document changes
		// In a full implementation, you would set up proper event sinks
		LogMessage(L"Word event handlers setup completed (simplified implementation)");
	}
	catch (...)
	{
		LogMessage(L"Exception in SetupWordEventHandlers");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::OnDocumentOpen()
{
	LogMessage(L"Document open event triggered");

	HRESULT hr = S_OK;

	try
	{
		// Create UI elements only when a document is open
		if (!m_bUICreated)
		{
			hr = CreateWordToolbar();
			if (SUCCEEDED(hr))
			{
				m_bUICreated = true;
				LogMessage(L"Word toolbar created successfully");
			}
			else
			{
				LogMessage(L"Failed to create Word toolbar");
			}
		}

		// Show document statistics
		hr = ShowWordStatistics();
		if (FAILED(hr))
		{
			LogMessage(L"Failed to show Word statistics");
		}

		LogMessage(L"Document open processing completed");
	}
	catch (...)
	{
		LogMessage(L"Exception in OnDocumentOpen");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::SetupExcelFeatures()
{
	// TODO: Implement Excel-specific features
	// - Cell count statistics
	// - Chart generation button
	return S_OK;
}

HRESULT COfficeAddin::SetupPowerPointFeatures()
{
	// TODO: Implement PowerPoint-specific features
	// - Text box count statistics
	// - Slide insertion with theme
	return S_OK;
}

HRESULT COfficeAddin::CreateWordToolbar()
{
	LogMessage(L"Creating Word UI elements...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		// For now, we'll create a simple menu item in the Add-ins tab
		// Modern Office uses Ribbon XML for custom UI, but we can add to existing menus

		// Get CommandBars collection
		DISPID dispid;
		OLECHAR* szMember = L"CommandBars";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
		{
			LogMessage(L"Failed to get CommandBars property - this is normal in modern Office");
			// This is expected in newer Office versions that use Ribbon
			return S_OK;
		}

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			LogMessage(L"CommandBars not available - using alternative approach");
			VariantClear(&result);
			return S_OK;
		}

		IDispatch* pCommandBars = result.pdispVal;

		// Try to add to the Tools menu or create a simple toolbar
		szMember = L"Add";
		hr = pCommandBars->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT args[2];
			VariantInit(&args[1]);
			VariantInit(&args[0]);
			args[1].vt = VT_BSTR;
			args[1].bstrVal = SysAllocString(L"MyCom Tools");
			args[0].vt = VT_I4;
			args[0].lVal = 1; // msoBarTop

			DISPPARAMS addParams;
			addParams.rgvarg = args;
			addParams.cArgs = 2;
			addParams.rgdispidNamedArgs = NULL;
			addParams.cNamedArgs = 0;

			VARIANT toolbarResult;
			VariantInit(&toolbarResult);

			hr = pCommandBars->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_METHOD, &addParams, &toolbarResult, NULL, NULL);
			if (SUCCEEDED(hr) && toolbarResult.vt == VT_DISPATCH)
			{
				m_pCommandBar = toolbarResult.pdispVal;
				m_pCommandBar->AddRef();

				// Add buttons to the toolbar
				hr = AddToolbarButtons();

				// Make toolbar visible
				szMember = L"Visible";
				hr = m_pCommandBar->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT visibleArg;
					VariantInit(&visibleArg);
					visibleArg.vt = VT_BOOL;
					visibleArg.boolVal = VARIANT_TRUE;

					DISPPARAMS visibleParams;
					visibleParams.rgvarg = &visibleArg;
					visibleParams.cArgs = 1;
					visibleParams.rgdispidNamedArgs = NULL;
					visibleParams.cNamedArgs = 0;

					m_pCommandBar->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &visibleParams, NULL, NULL, NULL);
				}

				LogMessage(L"Word toolbar created successfully");
			}

			VariantClear(&toolbarResult);
			VariantClear(&args[0]);
			VariantClear(&args[1]);
		}

		pCommandBars->Release();
		VariantClear(&result);
	}
	catch (...)
	{
		LogMessage(L"Exception in CreateWordToolbar");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::AddToolbarButtons()
{
	LogMessage(L"Adding toolbar buttons...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pCommandBar)
			return E_FAIL;

		// Get Controls collection
		DISPID dispid;
		OLECHAR* szMember = L"Controls";
		hr = m_pCommandBar->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
		{
			LogMessage(L"Failed to get Controls property");
			return hr;
		}

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pCommandBar->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			LogMessage(L"Failed to get Controls collection");
			VariantClear(&result);
			return hr;
		}

		IDispatch* pControls = result.pdispVal;

		// Add "Word Statistics" button
		szMember = L"Add";
		hr = pControls->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT args[1];
			VariantInit(&args[0]);
			args[0].vt = VT_I4;
			args[0].lVal = 1; // msoControlButton

			DISPPARAMS addParams;
			addParams.rgvarg = args;
			addParams.cArgs = 1;
			addParams.rgdispidNamedArgs = NULL;
			addParams.cNamedArgs = 0;

			VARIANT buttonResult;
			VariantInit(&buttonResult);

			hr = pControls->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_METHOD, &addParams, &buttonResult, NULL, NULL);
			if (SUCCEEDED(hr) && buttonResult.vt == VT_DISPATCH)
			{
				IDispatch* pButton = buttonResult.pdispVal;

				// Set button caption
				szMember = L"Caption";
				hr = pButton->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT captionArg;
					VariantInit(&captionArg);
					captionArg.vt = VT_BSTR;
					captionArg.bstrVal = SysAllocString(L"Word Statistics");

					DISPPARAMS captionParams;
					captionParams.rgvarg = &captionArg;
					captionParams.cArgs = 1;
					captionParams.rgdispidNamedArgs = NULL;
					captionParams.cNamedArgs = 0;

					pButton->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &captionParams, NULL, NULL, NULL);
					VariantClear(&captionArg);
				}

				// Set button tooltip
				szMember = L"TooltipText";
				hr = pButton->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT tooltipArg;
					VariantInit(&tooltipArg);
					tooltipArg.vt = VT_BSTR;
					tooltipArg.bstrVal = SysAllocString(L"Show document statistics including Chinese and English word count");

					DISPPARAMS tooltipParams;
					tooltipParams.rgvarg = &tooltipArg;
					tooltipParams.cArgs = 1;
					tooltipParams.rgdispidNamedArgs = NULL;
					tooltipParams.cNamedArgs = 0;

					pButton->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &tooltipParams, NULL, NULL, NULL);
					VariantClear(&tooltipArg);
				}

				pButton->Release();
			}

			VariantClear(&buttonResult);
			VariantClear(&args[0]);
		}

		// Add "Format Text" button
		hr = pControls->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT args[1];
			VariantInit(&args[0]);
			args[0].vt = VT_I4;
			args[0].lVal = 1; // msoControlButton

			DISPPARAMS addParams;
			addParams.rgvarg = args;
			addParams.cArgs = 1;
			addParams.rgdispidNamedArgs = NULL;
			addParams.cNamedArgs = 0;

			VARIANT buttonResult;
			VariantInit(&buttonResult);

			hr = pControls->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_METHOD, &addParams, &buttonResult, NULL, NULL);
			if (SUCCEEDED(hr) && buttonResult.vt == VT_DISPATCH)
			{
				IDispatch* pButton = buttonResult.pdispVal;

				// Set button caption
				szMember = L"Caption";
				hr = pButton->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT captionArg;
					VariantInit(&captionArg);
					captionArg.vt = VT_BSTR;
					captionArg.bstrVal = SysAllocString(L"Format Text");

					DISPPARAMS captionParams;
					captionParams.rgvarg = &captionArg;
					captionParams.cArgs = 1;
					captionParams.rgdispidNamedArgs = NULL;
					captionParams.cNamedArgs = 0;

					pButton->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &captionParams, NULL, NULL, NULL);
					VariantClear(&captionArg);
				}

				// Set button tooltip
				szMember = L"TooltipText";
				hr = pButton->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT tooltipArg;
					VariantInit(&tooltipArg);
					tooltipArg.vt = VT_BSTR;
					tooltipArg.bstrVal = SysAllocString(L"Format selected text with 2-character first line indent and 1.5x line spacing");

					DISPPARAMS tooltipParams;
					tooltipParams.rgvarg = &tooltipArg;
					tooltipParams.cArgs = 1;
					tooltipParams.rgdispidNamedArgs = NULL;
					tooltipParams.cNamedArgs = 0;

					pButton->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &tooltipParams, NULL, NULL, NULL);
					VariantClear(&tooltipArg);
				}

				pButton->Release();
			}

			VariantClear(&buttonResult);
			VariantClear(&args[0]);
		}

		pControls->Release();
		VariantClear(&result);

		LogMessage(L"Toolbar buttons added successfully");
	}
	catch (...)
	{
		LogMessage(L"Exception in AddToolbarButtons");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::ShowWordStatistics()
{
	LogMessage(L"Showing Word statistics...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
		{
			LogMessage(L"Word application not available");
			return E_FAIL;
		}

		// Check if there's an active document
		DISPID dispid;
		OLECHAR* szMember = L"ActiveDocument";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
		{
			LogMessage(L"Failed to get ActiveDocument property");
			MessageBox(NULL, L"No active document found.\nPlease open a document first.", L"MyCom - Word Statistics", MB_OK | MB_ICONWARNING);
			return hr;
		}

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			LogMessage(L"No active document available");
			MessageBox(NULL, L"No active document found.\nPlease open a document first.", L"MyCom - Word Statistics", MB_OK | MB_ICONWARNING);
			VariantClear(&result);
			return hr;
		}

		VariantClear(&result);

		// Get document text
		BSTR documentText = nullptr;
		hr = GetDocumentText(&documentText);

		if (SUCCEEDED(hr) && documentText)
		{
			int chineseCount = 0;
			int englishWords = 0;

			hr = CountChineseAndEnglishWords(documentText, chineseCount, englishWords);

			if (SUCCEEDED(hr))
			{
				wchar_t statsMessage[1024];
				int totalChars = (int)wcslen(documentText);

				// Remove control characters for more accurate count
				int printableChars = 0;
				for (int i = 0; i < totalChars; i++)
				{
					if (documentText[i] >= 32 && documentText[i] != 127) // Printable characters
						printableChars++;
				}

				swprintf_s(statsMessage, 1024,
					L"📊 Document Statistics\n\n"
					L"📝 Chinese Characters: %d\n"
					L"🔤 English Words: %d\n"
					L"📄 Total Characters: %d\n"
					L"✨ Printable Characters: %d\n\n"
					L"✅ Statistics generated successfully!\n\n"
					L"Tip: Use the 'Format Text' button to format selected text.",
					chineseCount, englishWords, totalChars, printableChars);

				MessageBox(NULL, statsMessage, L"MyCom - Word Document Statistics", MB_OK | MB_ICONINFORMATION | MB_TOPMOST);

				wchar_t logMsg[256];
				swprintf_s(logMsg, 256, L"Word statistics: Chinese=%d, English=%d, Total=%d, Printable=%d",
					chineseCount, englishWords, totalChars, printableChars);
				LogMessage(logMsg);
			}
			else
			{
				LogMessage(L"Failed to count words");
				MessageBox(NULL, L"Failed to analyze document text.", L"MyCom - Error", MB_OK | MB_ICONERROR);
			}

			SysFreeString(documentText);
		}
		else
		{
			LogMessage(L"Failed to get document text or document is empty");
			MessageBox(NULL, L"Document appears to be empty or could not be read.", L"MyCom - Word Statistics", MB_OK | MB_ICONINFORMATION);
		}
	}
	catch (...)
	{
		LogMessage(L"Exception in ShowWordStatistics");
		MessageBox(NULL, L"An error occurred while analyzing the document.", L"MyCom - Error", MB_OK | MB_ICONERROR);
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::GetDocumentText(BSTR* documentText)
{
	if (!documentText)
		return E_INVALIDARG;

	*documentText = nullptr;
	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
		{
			LogMessage(L"Word application not available in GetDocumentText");
			return E_FAIL;
		}

		// Get ActiveDocument
		DISPID dispid;
		OLECHAR* szMember = L"ActiveDocument";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
		{
			LogMessage(L"Failed to get ActiveDocument property ID");
			return hr;
		}

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT docResult;
		VariantInit(&docResult);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &docResult, NULL, NULL);
		if (FAILED(hr) || docResult.vt != VT_DISPATCH || docResult.pdispVal == nullptr)
		{
			LogMessage(L"Failed to get ActiveDocument or no document available");
			VariantClear(&docResult);
			return hr;
		}

		IDispatch* pDocument = docResult.pdispVal;
		LogMessage(L"Successfully got ActiveDocument");

		// Try to get the document content using Content property first
		szMember = L"Content";
		hr = pDocument->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT contentResult;
			VariantInit(&contentResult);

			hr = pDocument->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &contentResult, NULL, NULL);
			if (SUCCEEDED(hr) && contentResult.vt == VT_DISPATCH && contentResult.pdispVal != nullptr)
			{
				IDispatch* pContent = contentResult.pdispVal;
				LogMessage(L"Successfully got document Content");

				// Get Text property from Content
				szMember = L"Text";
				hr = pContent->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT textResult;
					VariantInit(&textResult);

					hr = pContent->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &textResult, NULL, NULL);
					if (SUCCEEDED(hr) && textResult.vt == VT_BSTR && textResult.bstrVal != nullptr)
					{
						*documentText = SysAllocString(textResult.bstrVal);
						LogMessage(L"Successfully extracted document text");
					}
					else
					{
						LogMessage(L"Failed to get text from Content or text is null");
					}

					VariantClear(&textResult);
				}
				else
				{
					LogMessage(L"Failed to get Text property ID from Content");
				}

				pContent->Release();
			}
			else
			{
				LogMessage(L"Failed to get Content property or Content is null");
			}

			VariantClear(&contentResult);
		}
		else
		{
			LogMessage(L"Failed to get Content property ID, trying Range method");

			// Fallback: try Range method with parameters
			szMember = L"Range";
			hr = pDocument->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
			if (SUCCEEDED(hr))
			{
				// Call Range() method with start=0, end=-1 (entire document)
				VARIANT args[2];
				VariantInit(&args[1]);
				VariantInit(&args[0]);
				args[1].vt = VT_I4;
				args[1].lVal = 0;  // Start position
				args[0].vt = VT_I4;
				args[0].lVal = -1; // End position (-1 means end of document)

				DISPPARAMS rangeParams;
				rangeParams.rgvarg = args;
				rangeParams.cArgs = 2;
				rangeParams.rgdispidNamedArgs = NULL;
				rangeParams.cNamedArgs = 0;

				VARIANT rangeResult;
				VariantInit(&rangeResult);

				hr = pDocument->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_METHOD, &rangeParams, &rangeResult, NULL, NULL);
				if (SUCCEEDED(hr) && rangeResult.vt == VT_DISPATCH && rangeResult.pdispVal != nullptr)
				{
					IDispatch* pRange = rangeResult.pdispVal;
					LogMessage(L"Successfully got document Range");

					// Get Text property from Range
					szMember = L"Text";
					hr = pRange->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
					if (SUCCEEDED(hr))
					{
						VARIANT textResult;
						VariantInit(&textResult);

						hr = pRange->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &textResult, NULL, NULL);
						if (SUCCEEDED(hr) && textResult.vt == VT_BSTR && textResult.bstrVal != nullptr)
						{
							*documentText = SysAllocString(textResult.bstrVal);
							LogMessage(L"Successfully extracted document text using Range");
						}
						else
						{
							LogMessage(L"Failed to get text from Range or text is null");
						}

						VariantClear(&textResult);
					}
					else
					{
						LogMessage(L"Failed to get Text property ID from Range");
					}

					pRange->Release();
				}
				else
				{
					LogMessage(L"Failed to get Range or Range is null");
				}

				VariantClear(&rangeResult);
				VariantClear(&args[0]);
				VariantClear(&args[1]);
			}
			else
			{
				LogMessage(L"Failed to get Range property ID");
			}
		}

		pDocument->Release();
		VariantClear(&docResult);
	}
	catch (...)
	{
		LogMessage(L"Exception in GetDocumentText");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::CountChineseAndEnglishWords(const wchar_t* text, int& chineseCount, int& englishWords)
{
	if (!text)
		return E_INVALIDARG;

	chineseCount = 0;
	englishWords = 0;

	try
	{
		int len = (int)wcslen(text);
		bool inEnglishWord = false;

		for (int i = 0; i < len; i++)
		{
			wchar_t ch = text[i];

			// Check if character is Chinese (CJK Unified Ideographs)
			if ((ch >= 0x4E00 && ch <= 0x9FFF) ||  // CJK Unified Ideographs
				(ch >= 0x3400 && ch <= 0x4DBF) ||  // CJK Extension A
				(ch >= 0x20000 && ch <= 0x2A6DF) || // CJK Extension B
				(ch >= 0x2A700 && ch <= 0x2B73F) || // CJK Extension C
				(ch >= 0x2B740 && ch <= 0x2B81F) || // CJK Extension D
				(ch >= 0x2B820 && ch <= 0x2CEAF))   // CJK Extension E
			{
				chineseCount++;
				inEnglishWord = false;
			}
			// Check if character is English letter
			else if ((ch >= L'A' && ch <= L'Z') || (ch >= L'a' && ch <= L'z'))
			{
				if (!inEnglishWord)
				{
					englishWords++;
					inEnglishWord = true;
				}
			}
			// Check if character is digit (count as part of English word)
			else if (ch >= L'0' && ch <= L'9')
			{
				if (!inEnglishWord)
				{
					englishWords++;
					inEnglishWord = true;
				}
			}
			// Other characters (spaces, punctuation, etc.)
			else
			{
				inEnglishWord = false;
			}
		}
	}
	catch (...)
	{
		return E_FAIL;
	}

	return S_OK;
}

HRESULT COfficeAddin::FormatSelectedText()
{
	LogMessage(L"Formatting selected text...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		// Get Selection object
		DISPID dispid;
		OLECHAR* szMember = L"Selection";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
			return hr;

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			VariantClear(&result);
			return hr;
		}

		IDispatch* pSelection = result.pdispVal;

		// Get ParagraphFormat
		szMember = L"ParagraphFormat";
		hr = pSelection->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT paraResult;
			VariantInit(&paraResult);

			hr = pSelection->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &paraResult, NULL, NULL);
			if (SUCCEEDED(hr) && paraResult.vt == VT_DISPATCH)
			{
				IDispatch* pParagraphFormat = paraResult.pdispVal;

				// Set FirstLineIndent to 2 characters (approximately 24 points)
				szMember = L"FirstLineIndent";
				hr = pParagraphFormat->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT indentArg;
					VariantInit(&indentArg);
					indentArg.vt = VT_R4;
					indentArg.fltVal = 24.0f; // 2 characters worth of indent

					DISPPARAMS indentParams;
					indentParams.rgvarg = &indentArg;
					indentParams.cArgs = 1;
					indentParams.rgdispidNamedArgs = NULL;
					indentParams.cNamedArgs = 0;

					pParagraphFormat->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &indentParams, NULL, NULL, NULL);
				}

				// Set LineSpacing to 1.5
				szMember = L"LineSpacing";
				hr = pParagraphFormat->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT spacingArg;
					VariantInit(&spacingArg);
					spacingArg.vt = VT_R4;
					spacingArg.fltVal = 18.0f; // 1.5 line spacing (12pt * 1.5)

					DISPPARAMS spacingParams;
					spacingParams.rgvarg = &spacingArg;
					spacingParams.cArgs = 1;
					spacingParams.rgdispidNamedArgs = NULL;
					spacingParams.cNamedArgs = 0;

					pParagraphFormat->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &spacingParams, NULL, NULL, NULL);
				}

				pParagraphFormat->Release();
				LogMessage(L"Text formatting applied successfully");

				MessageBox(NULL,
					L"Formatting Complete!\n\nApplied Format:\n- First Line Indent: 2 characters\n- Line Spacing: 1.5x\n\nFormat applied to selected text.",
					L"MyCom - Formatting Complete",
					MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
			}

			VariantClear(&paraResult);
		}

		pSelection->Release();
		VariantClear(&result);
	}
	catch (...)
	{
		LogMessage(L"Exception in FormatSelectedText");
		hr = E_FAIL;
	}

	return hr;
}
